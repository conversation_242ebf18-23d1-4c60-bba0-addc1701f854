# Biormika Stack Deployment Guide

This guide covers the complete deployment process for the Biormika EDF File Management System, including infrastructure, backend (Lambda), and frontend (S3 + CloudFront).

## Table of Contents
- [Architecture Overview](#architecture-overview)
- [Prerequisites](#prerequisites)
- [Initial Setup](#initial-setup)
- [Deployment Steps](#deployment-steps)
- [Post-Deployment Verification](#post-deployment-verification)
- [Updating and Redeployment](#updating-and-redeployment)
- [Troubleshooting](#troubleshooting)
- [Quick Commands Reference](#quick-commands-reference)

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                         CloudFront                           │
│                    (CDN Distribution)                        │
│                  https://dxxxxx.cloudfront.net               │
└──────────────────┬──────────────────────┬──────────────────┘
                   │                      │
                   ▼                      ▼
        ┌─────────────────┐    ┌─────────────────────┐
        │   S3 Bucket     │    │   API Gateway       │
        │ (Static Site)   │    │  + Lambda Function  │
        └─────────────────┘    └─────────────────────┘
                                          │
                                          ▼
                                ┌─────────────────┐
                                │   S3 Bucket     │
                                │ (EDF Storage)   │
                                └─────────────────┘
```

### Components:
- **Frontend**: React SPA served from S3 via CloudFront
- **Backend**: FastAPI running on AWS Lambda behind API Gateway
- **Storage**: S3 bucket for EDF file storage with versioning
- **Infrastructure**: Managed by AWS CDK (Python)

## Prerequisites

### 1. Required Tools
```bash
# Check if you have these installed
node --version          # Required: v20.19+
npm --version           # Required: v10+
python3 --version       # Required: 3.11+
pip3 --version          # Required: latest
aws --version           # Required: AWS CLI v2
cdk --version           # Required: v2.1023.0+
```

### 2. Install Missing Tools
```bash
# Install Node.js (using nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 20
nvm use 20

# Install AWS CLI
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /

# Install AWS CDK
npm install -g aws-cdk
```

### 3. AWS Account Setup
```bash
# Configure AWS credentials
aws configure --profile biormika

# Enter the following when prompted:
# AWS Access Key ID: [your-access-key]
# AWS Secret Access Key: [your-secret-key]
# Default region name: us-east-1
# Default output format: json

# Verify credentials
aws sts get-caller-identity --profile biormika
```

### 4. CDK Bootstrap (First time only)
```bash
cd Infra
cdk bootstrap aws://ACCOUNT_ID/us-east-1 --profile biormika
```

## Initial Setup

### 1. Clone Repository
```bash
git clone [repository-url]
cd biormika-stack
```

### 2. Install Dependencies

#### Frontend Dependencies
```bash
cd Frontend
npm install
```

#### Backend Dependencies
```bash
cd ../Backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

#### Infrastructure Dependencies
```bash
cd ../Infra
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt -r requirements-dev.txt
```

## Deployment Steps

### Step 1: Deploy Infrastructure (CDK)

This deploys S3 buckets, Lambda, API Gateway, and CloudFront.

```bash
cd Infra
source .venv/bin/activate

# Synthesize (validate) the stack
cdk synth

# Deploy the infrastructure
cdk deploy --profile biormika

# Or to auto-approve changes (use with caution)
cdk deploy --profile biormika --require-approval never

# Save outputs for later use
cdk deploy --profile biormika --outputs-file cdk-outputs.json
```

**Expected outputs:**
- S3 Storage Bucket Name
- Static Site Bucket Name
- CloudFront Distribution ID
- CloudFront URL
- API Gateway URL
- Lambda Function ARN

### Step 2: Build Lambda Deployment Package

The Lambda function needs all dependencies packaged for ARM64 architecture.

```bash
cd Backend

# Run the build script
chmod +x build_lambda.sh
./build_lambda.sh

# This creates lambda_deployment.zip (~21MB)
```

**Note**: The build script automatically:
- Installs dependencies for ARM64 architecture
- Includes the app directory and lambda_handler.py
- Creates a deployment-ready ZIP file

### Step 3: Deploy Backend (Automatic via CDK)

The backend is automatically deployed when you run `cdk deploy`. The CDK:
- Uses the lambda_deployment.zip if it exists
- Otherwise builds the package automatically
- Updates Lambda environment variables
- Configures API Gateway

### Step 4: Configure Frontend Environment

```bash
cd Frontend

# Create production environment file
cat > .env.production << EOF
VITE_API_BASE_URL=https://[your-api-gateway-id].execute-api.us-east-1.amazonaws.com/prod/api/v1
EOF

# Get the API URL from CDK outputs
grep "ApiGatewayUrl" ../Infra/cdk-outputs.json
```

### Step 5: Deploy Frontend

#### Option A: Using npm scripts (Recommended)
```bash
cd Frontend

# Build for production
npm run build:prod

# Deploy to S3
npm run deploy

# Invalidate CloudFront cache
npm run deploy:invalidate

# Or do everything in one command
npm run deploy:full
```

#### Option B: Using deployment script
```bash
cd Frontend
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

#### Option C: Manual deployment
```bash
cd Frontend

# Build production bundle
npm run build:prod

# Get bucket name from CDK outputs
BUCKET_NAME=$(grep "StaticSiteBucketName" ../Infra/cdk-outputs.json | cut -d'"' -f4)

# Sync to S3
aws s3 sync dist/ s3://$BUCKET_NAME --delete --profile biormika

# Get CloudFront distribution ID
DISTRIBUTION_ID=$(grep "DistributionId" ../Infra/cdk-outputs.json | cut -d'"' -f4)

# Invalidate CloudFront cache
aws cloudfront create-invalidation \
  --distribution-id $DISTRIBUTION_ID \
  --paths "/*" \
  --profile biormika
```

## Post-Deployment Verification

### 1. Check Infrastructure Status
```bash
# View all stacks
aws cloudformation list-stacks --profile biormika

# Check specific stack
aws cloudformation describe-stacks \
  --stack-name BiormikaStack \
  --profile biormika
```

### 2. Test API Endpoints
```bash
# Get API URL
API_URL=$(grep "ApiGatewayUrl" Infra/cdk-outputs.json | cut -d'"' -f4)

# Test health endpoint
curl ${API_URL}health

# Expected response: {"status":"healthy","service":"biormika-api"}

# Test files list
curl ${API_URL}api/v1/files/list

# Expected response: {"files":[],"total_count":0,"total_size":0}
```

### 3. Test Frontend
```bash
# Get CloudFront URL
CF_URL=$(grep "CloudFrontURL" Infra/cdk-outputs.json | cut -d'"' -f4)

# Open in browser
open $CF_URL  # macOS
# or
xdg-open $CF_URL  # Linux
```

### 4. Verify CORS Configuration
```bash
# Test CORS from CloudFront to API
curl -X OPTIONS ${API_URL}api/v1/files/list \
  -H "Origin: $CF_URL" \
  -H "Access-Control-Request-Method: GET" \
  -i | grep -i "access-control"
```

## Updating and Redeployment

### Update Backend Code
```bash
cd Backend
# Make your changes to the code

# Rebuild Lambda package
./build_lambda.sh

# Redeploy via CDK
cd ../Infra
source .venv/bin/activate
cdk deploy --profile biormika
```

### Update Frontend Code
```bash
cd Frontend
# Make your changes to the code

# Rebuild and deploy
npm run deploy:full
```

### Update Infrastructure
```bash
cd Infra
# Make changes to CDK code

# Check what will change
cdk diff --profile biormika

# Deploy changes
cdk deploy --profile biormika
```

## Troubleshooting

### Common Issues and Solutions

#### 1. CORS Errors
**Problem**: Getting CORS errors when frontend calls API
```bash
# Check Lambda environment variables
aws lambda get-function-configuration \
  --function-name BiormikaStack-LambdaApiApiFunction73FE819F-[hash] \
  --profile biormika \
  --query 'Environment.Variables.ALLOWED_ORIGINS'

# Should include your CloudFront URL
```

**Solution**: Redeploy CDK to update CORS configuration
```bash
cd Infra
cdk deploy --profile biormika
```

#### 2. 502 Bad Gateway
**Problem**: API returns 502 errors
```bash
# Check Lambda logs
aws logs tail /aws/lambda/[function-name] --profile biormika --since 10m
```

**Common causes**:
- Missing dependencies in Lambda package
- Wrong Python version (needs 3.11)
- Environment variables not set

**Solution**: Rebuild and redeploy Lambda
```bash
cd Backend
./build_lambda.sh
cd ../Infra
cdk deploy --profile biormika
```

#### 3. CloudFront Not Updating
**Problem**: Changes not reflected after deployment

**Solution**: Invalidate CloudFront cache
```bash
cd Frontend
npm run deploy:invalidate
```

#### 4. S3 Upload Failures
**Problem**: File uploads fail with CORS or permission errors

**Solution**: Check S3 bucket CORS
```bash
aws s3api get-bucket-cors \
  --bucket [bucket-name] \
  --profile biormika
```

#### 5. CDK Deployment Fails
**Problem**: CDK deploy fails with various errors

**Common solutions**:
```bash
# Clear CDK cache
rm -rf cdk.out

# Rebuild
cdk synth

# Try deploying again
cdk deploy --profile biormika
```

### Checking Logs

#### Lambda Function Logs
```bash
# List log groups
aws logs describe-log-groups --profile biormika

# Tail Lambda logs
aws logs tail /aws/lambda/[function-name] --profile biormika --follow
```

#### API Gateway Logs
```bash
# Enable API Gateway logging first via Console or CDK
# Then view logs in CloudWatch
```

## Quick Commands Reference

### Daily Development
```bash
# Start frontend dev server
cd Frontend && npm run dev

# Start backend locally
cd Backend && source venv/bin/activate && python run.py

# Deploy everything
cd Infra && cdk deploy --profile biormika
cd ../Frontend && npm run deploy:full
```

### Production Deployment
```bash
# Full deployment from scratch
cd Infra
source .venv/bin/activate
cdk deploy --profile biormika --outputs-file cdk-outputs.json

cd ../Backend
./build_lambda.sh

cd ../Frontend
npm run deploy:full
```

### Cleanup (Destroy Infrastructure)
```bash
# WARNING: This deletes all resources!
cd Infra
cdk destroy --profile biormika

# Confirm deletion when prompted
```

## Environment Variables

### Frontend (.env.production)
```bash
VITE_API_BASE_URL=https://[api-gateway-id].execute-api.us-east-1.amazonaws.com/prod/api/v1
```

### Backend (Lambda Environment)
Automatically set by CDK:
- `S3_BUCKET_NAME`: Storage bucket for EDF files
- `ALLOWED_ORIGINS`: Comma-separated list of allowed origins
- `LAMBDA_ENVIRONMENT`: "true"
- `S3_TRANSFER_ACCELERATION`: "true"

### Local Development (.env files)

Frontend (.env.development):
```bash
VITE_API_BASE_URL=http://localhost:8000/api/v1
```

Backend (.env):
```bash
S3_BUCKET_NAME=biormikastack-s3storageedfstoragebucket8b55a415-[hash]
```

## Security Considerations

1. **Never commit AWS credentials** to version control
2. **Use IAM roles** with least privilege principle
3. **Enable S3 versioning** for data protection (already configured)
4. **Keep CloudFront cache TTL appropriate** for your use case
5. **Monitor AWS costs** regularly
6. **Enable MFA** for AWS account
7. **Rotate access keys** periodically

## Monitoring and Maintenance

### Cost Monitoring
```bash
# Check current month costs
aws ce get-cost-and-usage \
  --time-period Start=2025-01-01,End=2025-01-31 \
  --granularity MONTHLY \
  --metrics "UnblendedCost" \
  --profile biormika
```

### CloudWatch Metrics
- Lambda invocations and errors
- API Gateway requests and latency
- S3 storage usage
- CloudFront requests and cache hit ratio

### Regular Maintenance Tasks
1. Review and clean up old S3 versions (monthly)
2. Update dependencies (quarterly)
3. Review CloudWatch logs for errors (weekly)
4. Monitor AWS costs (monthly)
5. Update CDK and AWS CLI tools (quarterly)

## Support and Resources

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/v2/guide/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)

## License

[Your License Here]

---

**Last Updated**: September 2025
**Version**: 1.0.0