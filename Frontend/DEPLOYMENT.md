# Frontend Deployment Guide

## Prerequisites

1. AWS CLI installed and configured
2. AWS profile `biormika` configured with appropriate permissions
3. Node.js and npm installed
4. jq installed (for parsing JSO<PERSON> in deployment script)

## Deployment Steps

### 1. Deploy Infrastructure (First Time Only)

```bash
cd ../Infra
source .venv/bin/activate
cdk deploy --profile biormika --outputs-file cdk-outputs.json
```

### 2. Deploy Frontend

#### Option A: Using the deployment script
```bash
cd Frontend
./scripts/deploy.sh
```

#### Option B: Manual deployment
```bash
# Build production bundle
npm run build:prod

# Get S3 bucket name and CloudFront ID from CDK outputs
export S3_BUCKET_NAME=$(jq -r '.BiormikaStack.StaticSiteSiteBucketName' ../Infra/cdk-outputs.json)
export CLOUDFRONT_DISTRIBUTION_ID=$(jq -r '.BiormikaStack.StaticSiteDistributionId' ../Infra/cdk-outputs.json)

# Deploy to S3
npm run deploy

# Invalidate CloudFront cache
npm run deploy:invalidate
```

#### Option C: Using npm scripts
```bash
# Set environment variables first
export S3_BUCKET_NAME=<your-bucket-name>
export CLOUDFRONT_DISTRIBUTION_ID=<your-distribution-id>

# Run full deployment
npm run deploy:full
```

## Environment Variables

The production build uses `.env.production`:
- `VITE_API_BASE_URL`: Points to the Lambda API Gateway endpoint

## Cache Strategy

- **Static assets** (JS, CSS): Cached for 1 year with cache-busting via hashed filenames
- **index.html**: No cache, always fetches latest version
- **CloudFront**: Invalidated after each deployment

## Architecture

The deployment creates:
1. **S3 Bucket**: Hosts the static files (private bucket)
2. **CloudFront Distribution**: CDN with Origin Access Control (OAC)
3. **Custom Error Responses**: Handles React Router by serving index.html for 404/403

## Troubleshooting

### Deployment fails with "bucket not found"
- Ensure CDK infrastructure is deployed first
- Check that cdk-outputs.json exists

### Site shows old content
- Wait for CloudFront invalidation to complete (2-5 minutes)
- Clear browser cache

### React Router paths return 404
- CloudFront is configured to handle this automatically
- Check custom error responses in CloudFront settings

## Monitoring

- **CloudFront Metrics**: Available in AWS Console
- **S3 Access Logs**: Can be enabled if needed
- **CloudWatch**: Monitor distribution performance

## Security

- S3 bucket is private (no public access)
- CloudFront uses Origin Access Control (OAC)
- HTTPS only (HTTP redirects to HTTPS)
- TLS 1.2+ enforced