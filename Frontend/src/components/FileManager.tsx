import React, { useEffect, useState } from "react";
import { Download, Trash2, FileIcon, RefreshCw, PlayCircle, CheckSquare, Square } from "lucide-react";
import { useFileOperations } from "@/hooks/useFileOperations";
import { useFileActions } from "@/hooks/useFileActions";
import { formatFileSize, formatDate } from "@/utils/file";
import { useToast } from "@/hooks/useToast";
import { DataTable, type TableColumn } from "./tables/DataTable";
import Toast from "./Toast";
import ConfirmDialog from "./ConfirmDialog";
import { useConfirmDialog } from "@/hooks/useConfirmDialog";
import { apiClient } from "@/services/api";
import type { FileInfo } from "@/types/api";

interface FileManagerProps {
  refreshTrigger?: number;
}

export const FileManager: React.FC<FileManagerProps> = ({
  refreshTrigger
}) => {
  const { toast, showToast, hideToast } = useToast();
  const { dialogState, showConfirmDialog } = useConfirmDialog();
  const { files, loading, error, fetchFiles } = useFileOperations();
  const { handleDownload, handleDelete, isOperationInProgress } = useFileActions({
    onDeleteSuccess: fetchFiles,
    showConfirmDialog,
  });

  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger, fetchFiles]);

  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(files.map(f => f.key));
    }
  };

  const handleSelectFile = (fileKey: string) => {
    setSelectedFiles(prev =>
      prev.includes(fileKey)
        ? prev.filter(k => k !== fileKey)
        : [...prev, fileKey]
    );
  };

  const handleProcessFiles = async () => {
    if (selectedFiles.length === 0) {
      showToast("Please select at least one file to process", "error");
      return;
    }

    setProcessing(true);
    try {
      await apiClient.post("/api/v1/analysis/batch", {
        file_keys: selectedFiles,
        parameters: {
          thresholds: {
            amplitude1: 2,
            amplitude2: 2,
            peaks1: 6,
            peaks2: 3,
            duration: 10,
            temporal_sync: 10,
            spatial_sync: 10
          },
          montage: { type: "bipolar" },
          frequency: { low_cutoff: 50, high_cutoff: 300 }
        }
      });

      showToast(
        `Processing ${selectedFiles.length} file(s). You'll receive an email when complete.`,
        "success"
      );

      setSelectedFiles([]);
    } catch (error) {
      showToast("Failed to submit files for processing", "error");
      console.error("Error processing files:", error);
    } finally {
      setProcessing(false);
    }
  };

  const columns: TableColumn<FileInfo>[] = [
    {
      key: "select",
      header: (
        <button
          onClick={handleSelectAll}
          className="p-1 hover:bg-gray-100 rounded"
          title={selectedFiles.length === files.length ? "Deselect All" : "Select All"}
        >
          {selectedFiles.length === files.length ? (
            <CheckSquare className="w-5 h-5 text-blue-600" />
          ) : (
            <Square className="w-5 h-5 text-gray-400" />
          )}
        </button>
      ),
      accessor: (file) => (
        <button
          onClick={() => handleSelectFile(file.key)}
          className="p-1 hover:bg-gray-100 rounded"
        >
          {selectedFiles.includes(file.key) ? (
            <CheckSquare className="w-5 h-5 text-blue-600" />
          ) : (
            <Square className="w-5 h-5 text-gray-400" />
          )}
        </button>
      ),
    },
    {
      key: "filename",
      header: "File Name",
      accessor: (file) => (
        <div className="flex items-center">
          <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">{file.filename}</span>
        </div>
      ),
    },
    {
      key: "size",
      header: "Size",
      accessor: (file) => (
        <span className="text-sm text-gray-500">{formatFileSize(file.size)}</span>
      ),
    },
    {
      key: "modified",
      header: "Modified",
      accessor: (file) => (
        <span className="text-sm text-gray-500">{formatDate(file.last_modified)}</span>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      className: "text-right",
      accessor: (file) => (
        <div className="flex justify-end gap-2">
          <button
            onClick={() => handleDownload(file.key)}
            disabled={isOperationInProgress("download", file.key)}
            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
            title="Download"
          >
            {isOperationInProgress("download", file.key) ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Download className="w-4 h-4" />
            )}
          </button>
          <button
            onClick={() => handleDelete(file.key, file.filename)}
            disabled={isOperationInProgress("delete", file.key)}
            className="text-red-600 hover:text-red-900 disabled:opacity-50"
            title="Delete"
          >
            {isOperationInProgress("delete", file.key) ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      {toast.isOpen && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
      <ConfirmDialog {...dialogState} />

      <div className="space-y-4">
        {/* Process Files Button */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckSquare className="w-5 h-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">
                {selectedFiles.length} file(s) selected
              </span>
            </div>
            <button
              onClick={handleProcessFiles}
              disabled={processing}
              className="flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {processing ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <PlayCircle className="w-4 h-4 mr-2" />
                  Process Selected Files
                </>
              )}
            </button>
          </div>
        )}

        <DataTable
          title={`Files Uploaded (Ready to be Analyzed) (${files.length})`}
          columns={columns}
          data={files}
          loading={loading}
          error={error}
          emptyMessage="No uploaded files available"
          onRefresh={fetchFiles}
          getRowKey={(file) => file.key}
        />
      </div>
    </>
  );
};