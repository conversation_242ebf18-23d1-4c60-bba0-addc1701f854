import React, { useEffect, useState, useCallback } from "react";
import Plot from "react-plotly.js";
import { Download, FileText, Activity, Clock, BarChart3 } from "lucide-react";
import { apiClient } from "@/services/api";

interface HFOEvent {
  channel: string;
  start_time: number;
  end_time: number;
  peak_frequency: number;
  amplitude: number;
}

interface AnalysisResults {
  metadata: {
    filename: string;
    sampling_rate: number;
    duration_seconds: number;
    channels: string[];
    processing_time: number;
  };
  statistics: {
    total_hfos: number;
    hfo_density: number;
    channels_with_hfos: string[];
    hfo_rate_per_channel?: Record<string, number>;
  };
  channel_data: Record<string, number[]>;
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}

interface ResultsViewerProps {
  jobId: string;
  onClose?: () => void;
}

export const ResultsViewer: React.FC<ResultsViewerProps> = ({ jobId, onClose }) => {
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);

  const fetchResults = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiClient.get<AnalysisResults>(`/api/v1/analysis/results/${jobId}`);
      setResults(response.data);

      // Set initial channel
      if (response.data.metadata.channels.length > 0) {
        setSelectedChannel(response.data.metadata.channels[0]);
      }
    } catch (err) {
      const error = err as { response?: { data?: { detail?: string } } };
      setError(error.response?.data?.detail || "Failed to load results");
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  const downloadResults = async (format: "json" | "csv") => {
    try {
      const response = await apiClient.get<{ download_url: string }>(
        `/api/v1/analysis/download/${jobId}?format=${format}`
      );
      window.open(response.data.download_url, "_blank");
    } catch (err) {
      console.error("Download failed:", err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  if (!results) return null;

  // Prepare data for Plotly visualization
  const plotData = selectedChannel && results.channel_data[selectedChannel] ? [
    {
      type: "scatter" as const,
      mode: "lines" as const,
      name: selectedChannel,
      x: Array.from(
        { length: results.channel_data[selectedChannel].length },
        (_, i) => i / results.metadata.sampling_rate
      ),
      y: results.channel_data[selectedChannel],
      line: { color: "#1e40af", width: 1 }
    },
    // Add HFO markers
    {
      type: "scatter" as const,
      mode: "markers" as const,
      name: "HFO Events",
      x: results.hfo_events
        .filter(hfo => hfo.channel === selectedChannel)
        .map(hfo => hfo.start_time),
      y: results.hfo_events
        .filter(hfo => hfo.channel === selectedChannel)
        .map(hfo => {
          const idx = Math.floor(hfo.start_time * results.metadata.sampling_rate);
          return results.channel_data[selectedChannel]?.[idx] || 0;
        }),
      marker: {
        color: "#dc2626",
        size: 10,
        symbol: "triangle-up"
      },
      text: results.hfo_events
        .filter(hfo => hfo.channel === selectedChannel)
        .map(hfo => `Freq: ${hfo.peak_frequency}Hz\nAmp: ${hfo.amplitude}`),
      hovertemplate: "%{text}<extra></extra>"
    }
  ] : [];

  // HFO distribution chart
  const hfoDistribution = results.statistics.hfo_rate_per_channel ? [
    {
      type: "bar" as const,
      x: Object.keys(results.statistics.hfo_rate_per_channel),
      y: Object.values(results.statistics.hfo_rate_per_channel),
      marker: { color: "#3b82f6" }
    }
  ] : [];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center border-b pb-4">
        <div>
          <h2 className="text-2xl font-semibold">HFO Analysis Results</h2>
          <p className="text-gray-600">{results.metadata.filename}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ×
          </button>
        )}
      </div>

      {/* Statistics Summary */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center text-blue-600 mb-2">
            <Activity className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">Total HFOs</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {results.statistics.total_hfos}
          </p>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center text-green-600 mb-2">
            <BarChart3 className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">HFO Density</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {results.statistics.hfo_density.toFixed(2)}/min
          </p>
        </div>

        <div className="bg-purple-50 p-4 rounded-lg">
          <div className="flex items-center text-purple-600 mb-2">
            <FileText className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">Channels</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {results.metadata.channels.length}
          </p>
        </div>

        <div className="bg-orange-50 p-4 rounded-lg">
          <div className="flex items-center text-orange-600 mb-2">
            <Clock className="w-5 h-5 mr-2" />
            <span className="text-sm font-medium">Duration</span>
          </div>
          <p className="text-2xl font-bold text-gray-900">
            {Math.round(results.metadata.duration_seconds / 60)} min
          </p>
        </div>
      </div>

      {/* Channel Selector */}
      <div className="flex items-center space-x-4">
        <label className="font-medium text-gray-700">Select Channel:</label>
        <select
          value={selectedChannel || ""}
          onChange={(e) => setSelectedChannel(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
        >
          {results.metadata.channels.map((channel) => (
            <option key={channel} value={channel}>
              {channel} ({results.hfo_events.filter(h => h.channel === channel).length} HFOs)
            </option>
          ))}
        </select>
      </div>

      {/* EEG Signal Visualization */}
      {selectedChannel && results.channel_data[selectedChannel] && (
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">
            EEG Signal with HFO Markers - {selectedChannel}
          </h3>
          <Plot
            data={plotData}
            layout={{
              height: 300,
              xaxis: {
                title: { text: "Time (seconds)" },
                range: timeWindow
              },
              yaxis: {
                title: { text: "Amplitude (μV)" }
              },
              showlegend: true,
              hovermode: "closest"
            }}
            config={{ responsive: true }}
            style={{ width: "100%" }}
          />

          {/* Time Window Controls */}
          <div className="mt-4 flex items-center space-x-4">
            <button
              onClick={() => setTimeWindow([Math.max(0, timeWindow[0] - 10), timeWindow[1] - 10])}
              disabled={timeWindow[0] <= 0}
              className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            >
              ← Previous
            </button>

            <span className="text-sm text-gray-600">
              {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
            </span>

            <button
              onClick={() => setTimeWindow([timeWindow[0] + 10, timeWindow[1] + 10])}
              disabled={timeWindow[1] >= results.metadata.duration_seconds}
              className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            >
              Next →
            </button>

            <input
              type="range"
              min="1"
              max="60"
              value={timeWindow[1] - timeWindow[0]}
              onChange={(e) => {
                const windowSize = parseInt(e.target.value);
                setTimeWindow([timeWindow[0], timeWindow[0] + windowSize]);
              }}
              className="ml-4"
            />
            <span className="text-sm text-gray-600">
              Window: {timeWindow[1] - timeWindow[0]}s
            </span>
          </div>
        </div>
      )}

      {/* HFO Distribution Chart */}
      {results.statistics.hfo_rate_per_channel && (
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">HFO Distribution by Channel</h3>
          <Plot
            data={hfoDistribution}
            layout={{
              height: 250,
              xaxis: { title: { text: "Channel" } },
              yaxis: { title: { text: "Number of HFOs" } },
              showlegend: false
            }}
            config={{ responsive: true }}
            style={{ width: "100%" }}
          />
        </div>
      )}

      {/* HFO Events Table */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">HFO Events Details</h3>
        <div className="overflow-x-auto max-h-64 overflow-y-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Channel
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Start Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Peak Freq
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                  Amplitude
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {results.hfo_events.slice(0, 50).map((hfo, idx) => (
                <tr key={idx} className="hover:bg-gray-50">
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                    {hfo.channel}
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                    {hfo.start_time.toFixed(3)}s
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                    {((hfo.end_time - hfo.start_time) * 1000).toFixed(1)}ms
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                    {hfo.peak_frequency}Hz
                  </td>
                  <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                    {hfo.amplitude.toFixed(1)}μV
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {results.hfo_events.length > 50 && (
            <p className="text-sm text-gray-500 mt-2 px-6">
              Showing 50 of {results.hfo_events.length} events
            </p>
          )}
        </div>
      </div>

      {/* Download Buttons */}
      <div className="flex justify-end space-x-3">
        <button
          onClick={() => downloadResults("csv")}
          className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          <Download className="w-4 h-4 mr-2" />
          Download CSV
        </button>
        <button
          onClick={() => downloadResults("json")}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Download className="w-4 h-4 mr-2" />
          Download Full Results
        </button>
      </div>
    </div>
  );
};