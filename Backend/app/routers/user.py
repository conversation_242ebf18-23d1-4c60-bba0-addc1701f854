"""
API endpoints for user preferences management.
"""

import os
import re
from datetime import datetime
from typing import Optional

import boto3
from botocore.exceptions import ClientError
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field, validator

from ..logging_config import get_logger

logger = get_logger(__name__)

# AWS clients
dynamodb = boto3.resource("dynamodb")

# Environment variables
PREFERENCES_TABLE_NAME = os.getenv("PREFERENCES_TABLE_NAME", "biormika-user-preferences")

# Table reference
preferences_table = dynamodb.Table(PREFERENCES_TABLE_NAME) if PREFERENCES_TABLE_NAME else None

router = APIRouter(prefix="/api/v1/user", tags=["user"])


# Request/Response models
class UserPreferences(BaseModel):
    """User preferences model."""
    user_id: str
    email: str
    notification_enabled: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    @validator("email")
    def validate_email(cls, v):
        """Validate email format."""
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, v):
            raise ValueError("Invalid email format")
        return v.lower()


class UpdatePreferencesRequest(BaseModel):
    """Request model for updating preferences."""
    email: str = Field(..., description="Notification email address")
    notification_enabled: bool = Field(True, description="Enable email notifications")

    @validator("email")
    def validate_email(cls, v):
        """Validate email format."""
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, v):
            raise ValueError("Invalid email format")
        return v.lower()


class PreferencesResponse(BaseModel):
    """Response model for preferences."""
    email: str
    notification_enabled: bool
    last_updated: str


@router.get("/preferences", response_model=PreferencesResponse)
async def get_user_preferences(user_id: str = "default_user"):
    """Get user preferences including email settings."""
    try:
        if not preferences_table:
            # Return default preferences if table not available
            return PreferencesResponse(
                email="<EMAIL>",
                notification_enabled=True,
                last_updated=datetime.utcnow().isoformat(),
            )

        # Get preferences from DynamoDB
        response = preferences_table.get_item(Key={"user_id": user_id})

        if "Item" not in response:
            # Return defaults if user not found
            return PreferencesResponse(
                email="",
                notification_enabled=True,
                last_updated=datetime.utcnow().isoformat(),
            )

        item = response["Item"]
        return PreferencesResponse(
            email=item.get("email", ""),
            notification_enabled=item.get("notification_enabled", True),
            last_updated=item.get("updated_at", item.get("created_at", "")),
        )

    except Exception as e:
        logger.error(f"Error getting user preferences: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/preferences", response_model=PreferencesResponse)
async def update_user_preferences(
    request: UpdatePreferencesRequest,
    user_id: str = "default_user"
):
    """Update user preferences including email settings."""
    try:
        if not preferences_table:
            # If table not available, just return the request
            return PreferencesResponse(
                email=request.email,
                notification_enabled=request.notification_enabled,
                last_updated=datetime.utcnow().isoformat(),
            )

        timestamp = datetime.utcnow().isoformat()

        # Check if user exists
        response = preferences_table.get_item(Key={"user_id": user_id})

        if "Item" not in response:
            # Create new user preferences
            preferences_table.put_item(
                Item={
                    "user_id": user_id,
                    "email": request.email,
                    "notification_enabled": request.notification_enabled,
                    "created_at": timestamp,
                    "updated_at": timestamp,
                }
            )
        else:
            # Update existing preferences
            preferences_table.update_item(
                Key={"user_id": user_id},
                UpdateExpression="SET email = :email, notification_enabled = :enabled, updated_at = :updated",
                ExpressionAttributeValues={
                    ":email": request.email,
                    ":enabled": request.notification_enabled,
                    ":updated": timestamp,
                }
            )

        logger.info(f"Updated preferences for user {user_id}")

        return PreferencesResponse(
            email=request.email,
            notification_enabled=request.notification_enabled,
            last_updated=timestamp,
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating user preferences: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/preferences/verify-email")
async def verify_email(email: str, user_id: str = "default_user"):
    """Verify email address (send test email)."""
    try:
        # Validate email format
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, email):
            raise HTTPException(status_code=400, detail="Invalid email format")

        # In production, this would send a verification email
        # For now, just validate and return success
        ses = boto3.client("ses")
        sender_email = os.getenv("SES_SENDER_EMAIL", "<EMAIL>")

        try:
            # Send test email
            ses.send_email(
                Source=sender_email,
                Destination={"ToAddresses": [email]},
                Message={
                    "Subject": {"Data": "Biormika - Email Verification"},
                    "Body": {
                        "Text": {
                            "Data": "Your email has been verified for Biormika HFO Analysis notifications.\n\n"
                                    "You will receive notifications when your analysis jobs complete."
                        }
                    }
                }
            )

            logger.info(f"Verification email sent to {email}")

            return {
                "status": "success",
                "message": "Verification email sent. Please check your inbox.",
            }

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'MessageRejected':
                raise HTTPException(
                    status_code=400,
                    detail="Email address not verified in SES. Please contact support."
                )
            raise

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying email: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats")
async def get_user_stats(user_id: str = "default_user"):
    """Get user analysis statistics."""
    try:
        # Get job statistics from analysis jobs table
        jobs_table_name = os.getenv("JOBS_TABLE_NAME", "biormika-analysis-jobs")
        jobs_table = dynamodb.Table(jobs_table_name)

        # Query jobs by user (would need GSI in production)
        # For now, return mock stats
        return {
            "total_analyses": 42,
            "completed_analyses": 38,
            "failed_analyses": 2,
            "processing_analyses": 2,
            "total_hfos_detected": 1234,
            "average_processing_time": 45.6,
            "last_analysis": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        # Return empty stats on error
        return {
            "total_analyses": 0,
            "completed_analyses": 0,
            "failed_analyses": 0,
            "processing_analyses": 0,
            "total_hfos_detected": 0,
            "average_processing_time": 0,
            "last_analysis": None,
        }