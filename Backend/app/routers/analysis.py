"""
API endpoints for HFO analysis job management.
"""

import json
import os
import uuid
from datetime import datetime
from typing import List, Optional

import boto3
from botocore.exceptions import ClientError
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from ..constants import MAX_FILE_SIZE_BYTES
from ..logging_config import get_logger

logger = get_logger(__name__)

# AWS clients
dynamodb = boto3.resource("dynamodb")
sqs = boto3.client("sqs")
s3 = boto3.client("s3")

# Environment variables
JOBS_TABLE_NAME = os.getenv("JOBS_TABLE_NAME", "biormika-analysis-jobs")
PREFERENCES_TABLE_NAME = os.getenv("PREFERENCES_TABLE_NAME", "biormika-user-preferences")
SQS_QUEUE_URL = os.getenv("SQS_QUEUE_URL", "")
S3_BUCKET_NAME = os.getenv("S3_BUCKET_NAME", "")

# Table references
jobs_table = dynamodb.Table(JOBS_TABLE_NAME) if JOBS_TABLE_NAME else None
preferences_table = dynamodb.Table(PREFERENCES_TABLE_NAME) if PREFERENCES_TABLE_NAME else None

router = APIRouter(prefix="/api/v1/analysis", tags=["analysis"])


# Request/Response models
class AnalysisRequest(BaseModel):
    """Request model for submitting analysis job."""
    file_key: str
    parameters: Optional[dict] = None


class BatchAnalysisRequest(BaseModel):
    """Request model for batch analysis."""
    file_keys: List[str]
    parameters: Optional[dict] = None


class AnalysisResponse(BaseModel):
    """Response model for analysis submission."""
    job_id: str
    status: str
    message: str


class BatchAnalysisResponse(BaseModel):
    """Response model for batch analysis."""
    batch_id: str
    job_ids: List[str]
    status: str
    message: str


class JobStatus(BaseModel):
    """Job status response model."""
    job_id: str
    status: str
    file_key: Optional[str] = None
    created_at: str
    updated_at: Optional[str] = None
    completed_at: Optional[str] = None
    hfo_count: Optional[int] = None
    results_url: Optional[str] = None
    error_message: Optional[str] = None


@router.post("/submit", response_model=AnalysisResponse)
async def submit_analysis(request: AnalysisRequest):
    """Submit a single EDF file for HFO analysis."""
    try:
        # Validate file exists in S3
        try:
            s3.head_object(Bucket=S3_BUCKET_NAME, Key=request.file_key)
        except ClientError:
            raise HTTPException(status_code=404, detail="File not found in storage")

        # Get user email from preferences (in production, get from auth context)
        user_email = await get_user_email("default_user")

        # Create job record
        job_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat()

        job_item = {
            "job_id": job_id,
            "file_key": request.file_key,
            "user_email": user_email,
            "status": "pending",
            "created_at": timestamp,
            "updated_at": timestamp,
            "parameters": request.parameters or {},
        }

        # Save to DynamoDB
        if jobs_table:
            jobs_table.put_item(Item=job_item)

        # Send message to SQS
        message = {
            "job_id": job_id,
            "file_key": request.file_key,
            "user_email": user_email,
            "parameters": request.parameters or {},
        }

        if SQS_QUEUE_URL:
            sqs.send_message(
                QueueUrl=SQS_QUEUE_URL,
                MessageBody=json.dumps(message),
            )

        logger.info(f"Submitted analysis job: {job_id}")

        return AnalysisResponse(
            job_id=job_id,
            status="pending",
            message="Analysis job submitted successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch", response_model=BatchAnalysisResponse)
async def submit_batch_analysis(request: BatchAnalysisRequest):
    """Submit multiple EDF files for batch HFO analysis."""
    try:
        # Validate all files exist
        for file_key in request.file_keys:
            try:
                s3.head_object(Bucket=S3_BUCKET_NAME, Key=file_key)
            except ClientError:
                raise HTTPException(
                    status_code=404,
                    detail=f"File not found: {file_key}"
                )

        # Get user email
        user_email = await get_user_email("default_user")

        # Create batch ID
        batch_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat()

        # Create individual jobs
        job_ids = []
        messages = []

        for file_key in request.file_keys:
            job_id = str(uuid.uuid4())
            job_ids.append(job_id)

            # Create job record
            job_item = {
                "job_id": job_id,
                "batch_id": batch_id,
                "file_key": file_key,
                "user_email": user_email,
                "status": "pending",
                "created_at": timestamp,
                "updated_at": timestamp,
                "parameters": request.parameters or {},
            }

            # Save to DynamoDB
            if jobs_table:
                jobs_table.put_item(Item=job_item)

            # Prepare SQS message
            messages.append({
                "Id": job_id,
                "MessageBody": json.dumps({
                    "job_id": job_id,
                    "batch_id": batch_id,
                    "file_key": file_key,
                    "user_email": user_email,
                    "parameters": request.parameters or {},
                }),
            })

        # Send batch messages to SQS (max 10 at a time)
        if SQS_QUEUE_URL and messages:
            for i in range(0, len(messages), 10):
                batch = messages[i:i+10]
                sqs.send_message_batch(
                    QueueUrl=SQS_QUEUE_URL,
                    Entries=batch,
                )

        logger.info(f"Submitted batch analysis: {batch_id} with {len(job_ids)} jobs")

        return BatchAnalysisResponse(
            batch_id=batch_id,
            job_ids=job_ids,
            status="pending",
            message=f"Batch analysis submitted with {len(job_ids)} files",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error submitting batch analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    """Get the status of an analysis job."""
    try:
        if not jobs_table:
            raise HTTPException(status_code=503, detail="Database not available")

        response = jobs_table.get_item(Key={"job_id": job_id})

        if "Item" not in response:
            raise HTTPException(status_code=404, detail="Job not found")

        item = response["Item"]

        return JobStatus(
            job_id=item["job_id"],
            status=item["status"],
            file_key=item.get("file_key"),
            created_at=item["created_at"],
            updated_at=item.get("updated_at"),
            completed_at=item.get("completed_at"),
            hfo_count=item.get("hfo_count"),
            results_url=item.get("results_url"),
            error_message=item.get("error_message"),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batch-status/{batch_id}")
async def get_batch_status(batch_id: str):
    """Get the status of all jobs in a batch."""
    try:
        if not jobs_table:
            raise HTTPException(status_code=503, detail="Database not available")

        # Query jobs by batch_id using GSI
        response = jobs_table.query(
            IndexName="BatchJobIndex",
            KeyConditionExpression="batch_id = :batch_id",
            ExpressionAttributeValues={":batch_id": batch_id},
        )

        if not response["Items"]:
            raise HTTPException(status_code=404, detail="Batch not found")

        # Summarize batch status
        jobs = response["Items"]
        total = len(jobs)
        completed = sum(1 for j in jobs if j["status"] == "completed")
        failed = sum(1 for j in jobs if j["status"] == "failed")
        processing = sum(1 for j in jobs if j["status"] == "processing")
        pending = sum(1 for j in jobs if j["status"] == "pending")

        return {
            "batch_id": batch_id,
            "total_jobs": total,
            "completed": completed,
            "failed": failed,
            "processing": processing,
            "pending": pending,
            "jobs": [
                {
                    "job_id": j["job_id"],
                    "file_key": j.get("file_key"),
                    "status": j["status"],
                    "hfo_count": j.get("hfo_count"),
                    "error": j.get("error_message"),
                }
                for j in jobs
            ],
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting batch status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{job_id}")
async def get_analysis_results(job_id: str):
    """Get the analysis results for a completed job."""
    try:
        # Get job status first
        status = await get_job_status(job_id)

        if status.status != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job is not completed. Current status: {status.status}"
            )

        # Get results from S3
        results_key = f"results/{job_id}/analysis_results.json"

        try:
            response = s3.get_object(Bucket=S3_BUCKET_NAME, Key=results_key)
            results = json.loads(response["Body"].read())

            # Generate presigned URLs for downloads
            results["download_urls"] = {
                "results_json": s3.generate_presigned_url(
                    "get_object",
                    Params={"Bucket": S3_BUCKET_NAME, "Key": results_key},
                    ExpiresIn=3600,
                ),
                "hfo_events_csv": s3.generate_presigned_url(
                    "get_object",
                    Params={
                        "Bucket": S3_BUCKET_NAME,
                        "Key": f"results/{job_id}/hfo_events.csv",
                    },
                    ExpiresIn=3600,
                ),
            }

            return results

        except ClientError:
            raise HTTPException(status_code=404, detail="Results not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis results: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/download/{job_id}")
async def download_results(job_id: str, format: str = Query("json", enum=["json", "csv"])):
    """Get a download link for analysis results."""
    try:
        # Verify job is completed
        status = await get_job_status(job_id)

        if status.status != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"Job is not completed. Current status: {status.status}"
            )

        # Generate presigned URL based on format
        if format == "csv":
            key = f"results/{job_id}/hfo_events.csv"
        else:
            key = f"results/{job_id}/analysis_results.json"

        presigned_url = s3.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": S3_BUCKET_NAME,
                "Key": key,
                "ResponseContentDisposition": f'attachment; filename="{job_id}_{format}"'
            },
            ExpiresIn=3600,
        )

        return {"download_url": presigned_url, "format": format}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating download link: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def get_user_email(user_id: str) -> str:
    """Get user email from preferences table."""
    try:
        if preferences_table:
            response = preferences_table.get_item(Key={"user_id": user_id})
            if "Item" in response and "email" in response["Item"]:
                return response["Item"]["email"]

        # Default email if not found
        return "<EMAIL>"

    except Exception as e:
        logger.warning(f"Error getting user email: {e}")
        return "<EMAIL>"