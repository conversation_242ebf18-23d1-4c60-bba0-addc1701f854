"""
File operations router
"""

from fastapi import APIRouter

from ..constants import DEFAULT_EXPIRY_SECONDS
from ..models import (
    ErrorResponse,
    FileDeleteRequest,
    FileDeleteResponse,
    FileListResponse,
)
from ..services.s3_service import s3_service
from ..utils.error_handlers import handle_not_found, handle_s3_error
from ..utils.file_utils import extract_filename_from_key

router = APIRouter(prefix="/files", tags=["files"])


# File listing
@router.get(
    "/list",
    response_model=FileListResponse,
    responses={500: {"model": ErrorResponse, "description": "Server error"}},
)
async def list_files(prefix: str | None = None):
    """List all files in S3 bucket"""
    try:
        files = s3_service.list_files(prefix)
        total_size = sum(file.size for file in files)

        return FileListResponse(
            files=files, total_count=len(files), total_size=total_size
        )
    except Exception as e:
        handle_s3_error("list files", e)


# File deletion
@router.delete(
    "/delete",
    response_model=FileDeleteResponse,
    responses={
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"},
    },
)
async def delete_file(request: FileDeleteRequest):
    """Delete file from S3 bucket"""
    try:
        metadata = s3_service.get_file_metadata(request.key)
        if not metadata:
            handle_not_found("File", request.key)

        success = s3_service.delete_file(request.key)

        return FileDeleteResponse(
            success=success,
            message="File deleted successfully",
            deleted_key=request.key,
        )
    except Exception as e:
        if hasattr(e, "status_code"):
            raise
        handle_s3_error("delete file", e, request.key)


# File download
@router.get(
    "/download/{key:path}",
    responses={
        200: {"description": "Download URL"},
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"},
    },
)
async def get_download_url(key: str):
    """Generate download URL for file"""
    try:
        metadata = s3_service.get_file_metadata(key)
        if not metadata:
            handle_not_found("File", key)

        url = s3_service.generate_download_url(key)

        return {
            "download_url": url,
            "expires_in": DEFAULT_EXPIRY_SECONDS,
            "filename": extract_filename_from_key(key),
            "size": metadata["size"],
        }
    except Exception as e:
        if hasattr(e, "status_code"):
            raise
        handle_s3_error("generate download URL", e, key)
