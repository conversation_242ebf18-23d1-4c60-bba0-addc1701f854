import os
from typing import Any, Dict

from pydantic import field_validator
from pydantic_settings import BaseSettings

from .constants import (
    ALLOWED_FILE_EXTENSIONS,
    AWS_PROFILE,
    AWS_REGION,
    DEFAULT_ALLOWED_ORIGINS,
    MAX_FILE_SIZE_MB,
    PRESIGNED_URL_EXPIRY_SECONDS,
)


class Settings(BaseSettings):
    aws_profile: str = AWS_PROFILE
    aws_region: str = AWS_REGION
    s3_bucket_name: str
    allowed_origins: str | list[str] = DEFAULT_ALLOWED_ORIGINS
    max_file_size_mb: int = MAX_FILE_SIZE_MB
    allowed_file_extensions: list[str] = ALLOWED_FILE_EXTENSIONS
    presigned_url_expiry_seconds: int = PRESIGNED_URL_EXPIRY_SECONDS
    s3_transfer_acceleration: bool = False

    # Lambda-specific settings
    lambda_environment: bool = False

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @field_validator('allowed_origins', mode='before')
    @classmethod
    def parse_allowed_origins(cls, v: Any) -> list[str]:
        if isinstance(v, str):
            # Parse comma-separated string
            return [origin.strip() for origin in v.split(',') if origin.strip()]
        elif isinstance(v, list):
            return v
        return DEFAULT_ALLOWED_ORIGINS

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Detect Lambda environment
        self.lambda_environment = (
            os.getenv("LAMBDA_ENVIRONMENT", "false").lower() == "true"
        )

        # Only set AWS_PROFILE in local development
        if not self.lambda_environment:
            os.environ["AWS_PROFILE"] = self.aws_profile

        # Read acceleration flag for all environments
        accel_env = os.getenv("S3_TRANSFER_ACCELERATION", "").lower()
        if accel_env:
            self.s3_transfer_acceleration = accel_env in ("true", "1", "yes")

    @property
    def is_lambda(self) -> bool:
        return self.lambda_environment


settings = Settings()
