"""
Main HFO processor for ECS Fargate.
Polls SQS queue, processes EDF files, and saves results to S3.
"""

import json
import logging
import os
import sys
import time
import traceback
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

import boto3
import numpy as np
from botocore.exceptions import ClientError

# Configure logging
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# AWS clients
s3_client = boto3.client("s3")
sqs_client = boto3.client("sqs")
dynamodb = boto3.resource("dynamodb")
ses_client = boto3.client("ses")

# Environment variables
SQS_QUEUE_URL = os.getenv("SQS_QUEUE_URL")
S3_BUCKET_NAME = os.getenv("S3_BUCKET_NAME")
JOBS_TABLE_NAME = os.getenv("JOBS_TABLE_NAME")
PREFERENCES_TABLE_NAME = os.getenv("PREFERENCES_TABLE_NAME")
SES_SENDER_EMAIL = os.getenv("SES_SENDER_EMAIL", "<EMAIL>")
FRONTEND_URL = os.getenv("FRONTEND_URL", "https://biormika.com")

# Table references
jobs_table = dynamodb.Table(JOBS_TABLE_NAME)
preferences_table = dynamodb.Table(PREFERENCES_TABLE_NAME)


class HFOProcessor:
    """Main processor class for HFO analysis."""

    def __init__(self):
        """Initialize the processor."""
        self.validate_environment()
        logger.info("HFO Processor initialized successfully")

    def validate_environment(self):
        """Validate required environment variables."""
        required_vars = [
            "SQS_QUEUE_URL",
            "S3_BUCKET_NAME",
            "JOBS_TABLE_NAME",
            "PREFERENCES_TABLE_NAME",
        ]
        missing = [var for var in required_vars if not os.getenv(var)]
        if missing:
            raise ValueError(f"Missing required environment variables: {missing}")

    def run(self):
        """Main processing loop."""
        logger.info("Starting HFO processor...")

        while True:
            try:
                # Poll SQS for messages
                messages = self.poll_queue()

                if messages:
                    for message in messages:
                        self.process_message(message)
                else:
                    # No messages, wait a bit
                    time.sleep(5)

            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                time.sleep(10)

    def poll_queue(self) -> list:
        """Poll SQS queue for messages."""
        try:
            response = sqs_client.receive_message(
                QueueUrl=SQS_QUEUE_URL,
                MaxNumberOfMessages=1,
                VisibilityTimeout=3600,  # 1 hour
                WaitTimeSeconds=20,  # Long polling
            )
            return response.get("Messages", [])
        except ClientError as e:
            logger.error(f"Error polling SQS: {e}")
            return []

    def process_message(self, message: dict):
        """Process a single SQS message."""
        receipt_handle = message["ReceiptHandle"]

        try:
            # Parse message body
            body = json.loads(message["Body"])
            job_id = body["job_id"]
            file_key = body["file_key"]
            user_email = body.get("user_email")
            parameters = body.get("parameters", {})

            logger.info(f"Processing job {job_id} for file {file_key}")

            # Update job status to processing
            self.update_job_status(job_id, "processing")

            # Download file from S3
            local_path = self.download_file(file_key, job_id)

            # Run HFO analysis
            results = self.run_hfo_analysis(local_path, parameters)

            # Save results to S3
            results_url = self.save_results(job_id, results)

            # Update job as completed
            self.update_job_status(
                job_id,
                "completed",
                results_url=results_url,
                hfo_count=results.get("statistics", {}).get("total_hfos", 0),
            )

            # Send completion email
            if user_email:
                self.send_completion_email(job_id, user_email, file_key, results)

            # Delete message from queue
            sqs_client.delete_message(
                QueueUrl=SQS_QUEUE_URL,
                ReceiptHandle=receipt_handle,
            )

            # Clean up local files
            self.cleanup_local_files(job_id)

            logger.info(f"Successfully processed job {job_id}")

        except Exception as e:
            logger.error(f"Error processing message: {e}\n{traceback.format_exc()}")

            # Update job as failed
            if "job_id" in locals():
                self.update_job_status(
                    job_id,
                    "failed",
                    error=str(e),
                )

                # Send error email if we have user email
                if "user_email" in locals() and user_email:
                    self.send_error_email(job_id, user_email, file_key, str(e))

            # Don't delete the message - let it retry or go to DLQ

    def download_file(self, file_key: str, job_id: str) -> str:
        """Download EDF file from S3."""
        local_path = f"/tmp/edf_processing/{job_id}.edf"
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        logger.info(f"Downloading {file_key} to {local_path}")
        s3_client.download_file(S3_BUCKET_NAME, file_key, local_path)

        return local_path

    def run_hfo_analysis(self, file_path: str, parameters: dict) -> dict:
        """Run HFO analysis on the EDF file."""
        logger.info(f"Running HFO analysis on {file_path}")

        # Import HFO analysis modules (these will be copied from biormika-http-backend)
        try:
            # Add the core module to path
            sys.path.append("/app")
            from core.hfo_processor_standalone import process_edf_file

            # Process the EDF file
            results = process_edf_file(
                file_path,
                parameters.get("thresholds", {}),
                parameters.get("frequency", {"low_cutoff": 50, "high_cutoff": 300}),
                parameters.get("montage", {"type": "bipolar"}),
            )

            return results

        except ImportError as e:
            logger.error(f"Import error: {e}")
            # Fallback: create mock results for testing
            return self.create_mock_results()

    def create_mock_results(self) -> dict:
        """Create mock results for testing when HFO engine is not available."""
        logger.warning("Using mock results - HFO engine not available")

        return {
            "metadata": {
                "sampling_rate": 256,
                "duration_seconds": 600,
                "channels": ["FP1-FP2", "F3-F4", "C3-C4"],
                "processing_time": 45.2,
            },
            "statistics": {
                "total_hfos": 127,
                "hfo_density": 0.21,
                "channels_with_hfos": ["FP1-FP2", "F3-F4"],
            },
            "channel_data": {
                "FP1-FP2": list(np.random.randn(1000).tolist()),
                "F3-F4": list(np.random.randn(1000).tolist()),
                "C3-C4": list(np.random.randn(1000).tolist()),
            },
            "hfo_events": [
                {
                    "channel": "FP1-FP2",
                    "start_time": 10.5,
                    "end_time": 10.52,
                    "peak_frequency": 150,
                    "amplitude": 25.3,
                }
                for _ in range(10)
            ],
        }

    def save_results(self, job_id: str, results: dict) -> str:
        """Save analysis results to S3."""
        # Save main results JSON
        results_key = f"results/{job_id}/analysis_results.json"

        logger.info(f"Saving results to s3://{S3_BUCKET_NAME}/{results_key}")

        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=results_key,
            Body=json.dumps(results),
            ContentType="application/json",
        )

        # Generate CSV of HFO events
        if results.get("hfo_events"):
            csv_content = self.generate_hfo_csv(results["hfo_events"])
            csv_key = f"results/{job_id}/hfo_events.csv"

            s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=csv_key,
                Body=csv_content,
                ContentType="text/csv",
            )

        # Generate presigned URL for results
        presigned_url = s3_client.generate_presigned_url(
            "get_object",
            Params={"Bucket": S3_BUCKET_NAME, "Key": results_key},
            ExpiresIn=86400,  # 24 hours
        )

        return presigned_url

    def generate_hfo_csv(self, hfo_events: list) -> str:
        """Generate CSV content from HFO events."""
        import csv
        import io

        output = io.StringIO()
        writer = csv.DictWriter(
            output,
            fieldnames=["channel", "start_time", "end_time", "peak_frequency", "amplitude"],
        )
        writer.writeheader()
        writer.writerows(hfo_events)

        return output.getvalue()

    def update_job_status(
        self,
        job_id: str,
        status: str,
        results_url: Optional[str] = None,
        hfo_count: Optional[int] = None,
        error: Optional[str] = None,
    ):
        """Update job status in DynamoDB."""
        update_expr = "SET #status = :status, updated_at = :updated_at"
        expr_values = {
            ":status": status,
            ":updated_at": datetime.utcnow().isoformat(),
        }

        if status == "completed":
            update_expr += ", completed_at = :completed_at, results_url = :results_url, hfo_count = :hfo_count"
            expr_values[":completed_at"] = datetime.utcnow().isoformat()
            expr_values[":results_url"] = results_url
            expr_values[":hfo_count"] = hfo_count

        elif status == "failed":
            update_expr += ", error_message = :error"
            expr_values[":error"] = error

        jobs_table.update_item(
            Key={"job_id": job_id},
            UpdateExpression=update_expr,
            ExpressionAttributeNames={"#status": "status"},
            ExpressionAttributeValues=expr_values,
        )

    def send_completion_email(
        self, job_id: str, user_email: str, file_key: str, results: dict
    ):
        """Send completion email via SES."""
        try:
            filename = os.path.basename(file_key)
            results_url = f"{FRONTEND_URL}/analysis/results/{job_id}"
            download_url = f"{FRONTEND_URL}/api/v1/analysis/download/{job_id}"

            template_data = {
                "job_id": job_id,
                "filename": filename,
                "hfo_count": str(results.get("statistics", {}).get("total_hfos", 0)),
                "channel_count": str(len(results.get("metadata", {}).get("channels", []))),
                "processing_time": str(results.get("metadata", {}).get("processing_time", 0)),
                "hfo_density": str(results.get("statistics", {}).get("hfo_density", 0)),
                "results_url": results_url,
                "download_url": download_url,
                "completed_at": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            ses_client.send_templated_email(
                Source=SES_SENDER_EMAIL,
                Destination={"ToAddresses": [user_email]},
                Template="biormika-analysis-complete",
                TemplateData=json.dumps(template_data),
            )

            logger.info(f"Sent completion email to {user_email} for job {job_id}")

        except ClientError as e:
            logger.error(f"Failed to send email: {e}")

    def send_error_email(self, job_id: str, user_email: str, file_key: str, error: str):
        """Send error notification email."""
        try:
            filename = os.path.basename(file_key)

            template_data = {
                "job_id": job_id,
                "filename": filename,
                "error_message": error,
                "failed_at": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            ses_client.send_templated_email(
                Source=SES_SENDER_EMAIL,
                Destination={"ToAddresses": [user_email]},
                Template="biormika-analysis-error",
                TemplateData=json.dumps(template_data),
            )

            logger.info(f"Sent error email to {user_email} for job {job_id}")

        except ClientError as e:
            logger.error(f"Failed to send error email: {e}")

    def cleanup_local_files(self, job_id: str):
        """Clean up local temporary files."""
        try:
            local_path = f"/tmp/edf_processing/{job_id}.edf"
            if os.path.exists(local_path):
                os.remove(local_path)
                logger.info(f"Cleaned up local file for job {job_id}")
        except Exception as e:
            logger.warning(f"Failed to cleanup local files: {e}")


def main():
    """Main entry point."""
    processor = HFOProcessor()
    processor.run()


if __name__ == "__main__":
    main()