# Biormika Infrastructure

AWS CDK infrastructure for the Biormika EDF file upload and management system.

## Architecture

- **S3 Bucket**: Secure storage for EDF files (up to 1GB)
- **IAM Roles**: Backend service role with permissions to generate presigned URLs
- **CORS Configuration**: Allows uploads from frontend application

## Prerequisites

- AWS CLI configured with `biormika` profile
- Python 3.8 or higher
- Node.js 18.x or higher (for CDK CLI)

## Setup

1. Create and activate virtual environment:
```bash
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Bootstrap CDK (first time only):
```bash
cdk bootstrap --profile biormika
```

## Deployment

Deploy the infrastructure:
```bash
cdk deploy --profile biormika
```

## Outputs

After deployment, the stack outputs:
- `BucketName`: S3 bucket name for EDF file storage
- `BucketArn`: ARN of the S3 bucket
- `BackendRoleArn`: IAM role ARN for backend service

## Useful Commands

- `cdk ls` - List all stacks
- `cdk synth` - Synthesize CloudFormation template
- `cdk diff` - Compare deployed stack with current state
- `cdk destroy` - Remove all resources

## Security Features

- S3 bucket with server-side encryption
- Versioning enabled for file recovery
- All public access blocked
- CORS configured for specific origins only
- IAM role with least-privilege permissions