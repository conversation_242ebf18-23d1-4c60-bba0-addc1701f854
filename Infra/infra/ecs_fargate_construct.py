"""
ECS Fargate infrastructure for HFO analysis processing.
"""

from aws_cdk import (
    Duration,
    RemovalPolicy,
    aws_ecs as ecs,
    aws_ec2 as ec2,
    aws_iam as iam,
    aws_logs as logs,
    aws_applicationautoscaling as autoscaling,
    aws_ecr as ecr,
    aws_sqs as sqs,
    aws_s3 as s3,
    aws_dynamodb as dynamodb,
)
from constructs import Construct


class ECSFargateConstruct(Construct):
    """Create ECS Fargate infrastructure for HFO processing."""

    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        vpc: ec2.IVpc,
        job_queue: sqs.IQueue,
        s3_bucket: s3.IBucket,
        jobs_table: dynamodb.ITable,
        preferences_table: dynamodb.ITable,
        **kwargs
    ) -> None:
        super().__init__(scope, construct_id)

        # Create ECR repository for HFO processor image
        self.repository = ecr.Repository(
            self,
            "HFOProcessorRepository",
            repository_name="biormika-hfo-processor",
            removal_policy=RemovalPolicy.DESTROY,  # For dev
            lifecycle_rules=[
                ecr.LifecycleRule(
                    max_image_count=10,  # Keep only last 10 images
                    description="Remove old images",
                )
            ],
        )

        # Create ECS cluster
        self.cluster = ecs.Cluster(
            self,
            "HFOProcessingCluster",
            cluster_name="biormika-hfo-cluster",
            vpc=vpc,
            container_insights=True,  # Enable CloudWatch Container Insights
        )

        # Task execution role (for pulling images, writing logs)
        self.task_execution_role = iam.Role(
            self,
            "TaskExecutionRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AmazonECSTaskExecutionRolePolicy"
                )
            ],
        )

        # Task role (for application permissions)
        self.task_role = iam.Role(
            self,
            "TaskRole",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com"),
        )

        # Grant permissions to task role
        job_queue.grant_consume_messages(self.task_role)
        s3_bucket.grant_read_write(self.task_role)
        jobs_table.grant_read_write_data(self.task_role)
        preferences_table.grant_read_data(self.task_role)

        # Create log group
        self.log_group = logs.LogGroup(
            self,
            "HFOProcessorLogs",
            log_group_name="/ecs/biormika-hfo-processor",
            retention=logs.RetentionDays.ONE_WEEK,
            removal_policy=RemovalPolicy.DESTROY,
        )

        # Task definition
        self.task_definition = ecs.FargateTaskDefinition(
            self,
            "HFOProcessorTaskDef",
            cpu=4096,  # 4 vCPU
            memory_limit_mib=8192,  # 8 GB
            execution_role=self.task_execution_role,
            task_role=self.task_role,
            family="biormika-hfo-processor",
        )

        # Container definition
        self.container = self.task_definition.add_container(
            "HFOProcessor",
            image=ecs.ContainerImage.from_ecr_repository(self.repository, "latest"),
            logging=ecs.LogDrivers.aws_logs(
                stream_prefix="hfo-processor",
                log_group=self.log_group,
            ),
            environment={
                "AWS_DEFAULT_REGION": scope.region,
                "SQS_QUEUE_URL": job_queue.queue_url,
                "S3_BUCKET_NAME": s3_bucket.bucket_name,
                "JOBS_TABLE_NAME": jobs_table.table_name,
                "PREFERENCES_TABLE_NAME": preferences_table.table_name,
                "LOG_LEVEL": "INFO",
            },
            cpu=4096,
            memory_limit_mib=8192,
            essential=True,
        )

        # Create service
        self.service = ecs.FargateService(
            self,
            "HFOProcessorService",
            cluster=self.cluster,
            task_definition=self.task_definition,
            service_name="biormika-hfo-processor",
            desired_count=0,  # Start with 0, auto-scale based on queue
            assign_public_ip=True,  # Needed for ECR access in public subnet
            capacity_provider_strategies=[
                ecs.CapacityProviderStrategy(
                    capacity_provider="FARGATE_SPOT",
                    weight=4,  # Prefer Spot instances
                ),
                ecs.CapacityProviderStrategy(
                    capacity_provider="FARGATE",
                    weight=1,  # Fallback to on-demand
                ),
            ],
        )

        # Auto-scaling based on SQS queue depth
        scaling_target = self.service.auto_scale_task_count(
            min_capacity=0,
            max_capacity=10,
        )

        # Scale based on queue depth
        scaling_target.scale_on_metric(
            "QueueDepthScaling",
            metric=job_queue.metric_approximate_number_of_messages_visible(),
            scaling_steps=[
                autoscaling.ScalingInterval(upper=0, change=-1),  # Scale to 0 when empty
                autoscaling.ScalingInterval(lower=1, upper=5, change=1),  # 1 task for 1-5 messages
                autoscaling.ScalingInterval(lower=5, upper=10, change=2),  # 2 additional tasks for 5-10
                autoscaling.ScalingInterval(lower=10, upper=20, change=3),  # More aggressive scaling
                autoscaling.ScalingInterval(lower=20, change=5),  # Max scaling for high load
            ],
            adjustment_type=autoscaling.AdjustmentType.EXACT_CAPACITY,
            cooldown=Duration.minutes(2),
            evaluation_periods=1,
        )

        # Also scale based on oldest message age (backup metric)
        scaling_target.scale_on_metric(
            "OldestMessageAgeScaling",
            metric=job_queue.metric_approximate_age_of_oldest_message(),
            scaling_steps=[
                autoscaling.ScalingInterval(upper=300, change=0),  # No scale if < 5 min
                autoscaling.ScalingInterval(lower=300, upper=600, change=2),  # Scale if > 5 min
                autoscaling.ScalingInterval(lower=600, change=5),  # Aggressive if > 10 min
            ],
            adjustment_type=autoscaling.AdjustmentType.CHANGE_IN_CAPACITY,
            cooldown=Duration.minutes(5),
            evaluation_periods=2,
        )

    def get_repository_uri(self) -> str:
        """Get the ECR repository URI for docker push."""
        return self.repository.repository_uri