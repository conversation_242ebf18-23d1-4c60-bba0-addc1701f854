from aws_cdk import Stack, Tags
from aws_cdk import aws_ec2 as ec2
from constructs import Construct

from .config import DEFAULT_ALLOWED_ORIGINS, ENVIRONMENT, PROJECT_NAME, PURPOSE
from .dynamodb_construct import Dynamo<PERSON><PERSON><PERSON>truct
from .ecs_fargate_construct import ECSFargateConstruct
from .lambda_construct import LambdaApiConstruct
from .s3_construct import S3StorageConstruct
from .ses_construct import SESConstruct
from .sqs_construct import SQSConstruct
from .static_site_construct import StaticSiteConstruct


class InfraStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # Create static site infrastructure FIRST
        # so we can get its CloudFront URL for CORS configuration
        static_site = StaticSiteConstruct(
            self,
            "StaticSite",
            domain_name=None,
            certificate_arn=None,
        )

        # Build allowed origins list including CloudFront URL
        allowed_origins = list(DEFAULT_ALLOWED_ORIGINS)
        # Add CloudFront URL to allowed origins for both S3 and Lambda
        cloudfront_url = f"https://{static_site.distribution.distribution_domain_name}"
        allowed_origins.append(cloudfront_url)

        # Create S3 storage infrastructure with CloudFront origin
        s3_storage = S3StorageConstruct(
            self, "S3Storage",
            bucket_name=None,
            allowed_origins=allowed_origins  # Pass CloudFront URL here
        )

        # Create Lambda API infrastructure with updated origins
        lambda_api = LambdaApiConstruct(
            self,
            "LambdaApi",
            bucket_name=s3_storage.edf_bucket.bucket_name,
            backend_role=s3_storage.backend_role,
            allowed_origins=allowed_origins,
        )

        # Create DynamoDB tables for user preferences and job tracking
        dynamodb = DynamoDBConstruct(self, "DynamoDB")

        # Create SQS queue for job processing
        sqs = SQSConstruct(self, "SQS")

        # Create SES for email notifications
        ses = SESConstruct(
            self,
            "SES",
            sender_email="<EMAIL>"  # Update with your verified email
        )

        # Create VPC for ECS Fargate (or use default)
        vpc = ec2.Vpc.from_lookup(
            self,
            "VPC",
            is_default=True
        )

        # Create ECS Fargate infrastructure for HFO processing
        ecs_fargate = ECSFargateConstruct(
            self,
            "ECSFargate",
            vpc=vpc,
            job_queue=sqs.job_queue,
            s3_bucket=s3_storage.edf_bucket,
            jobs_table=dynamodb.analysis_jobs_table,
            preferences_table=dynamodb.user_preferences_table,
        )

        # Grant Lambda permissions for new services
        dynamodb.grant_read_write(lambda_api.api_function)
        sqs.grant_send_messages(lambda_api.api_function)
        ses.grant_send_email(lambda_api.api_function)

        # Add environment variables to Lambda
        lambda_api.api_function.add_environment("JOBS_TABLE_NAME", dynamodb.analysis_jobs_table.table_name)
        lambda_api.api_function.add_environment("PREFERENCES_TABLE_NAME", dynamodb.user_preferences_table.table_name)
        lambda_api.api_function.add_environment("SQS_QUEUE_URL", sqs.job_queue.queue_url)
        lambda_api.api_function.add_environment("SES_SENDER_EMAIL", ses.sender_email)

        # Add stack tags
        Tags.of(self).add("Project", PROJECT_NAME)
        Tags.of(self).add("Environment", ENVIRONMENT)
        Tags.of(self).add("Purpose", PURPOSE)
