from aws_cdk import Duration, RemovalPolicy
from aws_cdk import aws_iam as iam
from aws_cdk import aws_s3 as s3
from constructs import Construct

from .config import (
    ALLOWED_FILE_PATTERNS,
    DEFAULT_ALLOWED_ORIGINS,
    S3_MULTIPART_UPLOAD_CLEANUP_DAYS,
)
from .helpers import create_cfn_output, create_s3_cors_rules, create_s3_presigned_policy


class S3StorageConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        bucket_name: str | None = None,
        allowed_origins: list[str] | None = None,
    ) -> None:
        super().__init__(scope, construct_id)

        self.allowed_origins = allowed_origins or DEFAULT_ALLOWED_ORIGINS

        # Create S3 bucket
        self._create_s3_bucket(bucket_name)

        # Create IAM role
        self._create_backend_role()

        # Grant permissions
        self._grant_permissions()

        # Create outputs
        self._create_outputs()

    def _create_s3_bucket(self, bucket_name: str | None) -> None:
        """Create S3 bucket with security and lifecycle configurations."""
        self.edf_bucket = s3.Bucket(
            self,
            "EDFStorageBucket",
            bucket_name=bucket_name,
            versioned=True,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            lifecycle_rules=self._get_lifecycle_rules(),
            cors=create_s3_cors_rules(self.allowed_origins),
            transfer_acceleration=True,
            removal_policy=RemovalPolicy.RETAIN,
        )

    def _get_lifecycle_rules(self) -> list[s3.LifecycleRule]:
        """Get S3 bucket lifecycle rules."""
        return [
            s3.LifecycleRule(
                id="DeleteIncompleteMultipartUploads",
                abort_incomplete_multipart_upload_after=Duration.days(
                    S3_MULTIPART_UPLOAD_CLEANUP_DAYS
                ),
            )
        ]

    def _create_backend_role(self) -> None:
        """Create IAM role for backend service."""
        self.backend_role = iam.Role(
            self,
            "BackendServiceRole",
            assumed_by=iam.CompositePrincipal(
                iam.ServicePrincipal("ec2.amazonaws.com"),
                iam.ServicePrincipal("lambda.amazonaws.com"),
            ),
            description="Role for backend service to access S3 for EDF file operations",
        )

    def _grant_permissions(self) -> None:
        """Grant S3 permissions to the backend role."""
        # Grant read/write permissions
        self.edf_bucket.grant_read_write(self.backend_role)

        # Add policy for presigned URLs
        self.backend_role.add_to_policy(
            create_s3_presigned_policy(
                self.edf_bucket.bucket_arn, ALLOWED_FILE_PATTERNS
            )
        )

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs."""
        create_cfn_output(
            self,
            "BucketName",
            value=self.edf_bucket.bucket_name,
            description="Name of the S3 bucket for EDF file storage",
        )

        create_cfn_output(
            self,
            "BucketArn",
            value=self.edf_bucket.bucket_arn,
            description="ARN of the S3 bucket",
        )

        create_cfn_output(
            self,
            "BackendRoleArn",
            value=self.backend_role.role_arn,
            description="ARN of the backend service role",
        )
