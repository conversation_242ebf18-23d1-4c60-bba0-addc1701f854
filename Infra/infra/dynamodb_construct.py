"""
DynamoDB tables for HFO analysis job tracking and user preferences.
"""

from aws_cdk import (
    Duration,
    RemovalPolicy,
    aws_dynamodb as dynamodb,
    aws_iam as iam,
)
from constructs import Construct


class DynamoDBConstruct(Construct):
    """Create DynamoDB tables for analysis jobs and user preferences."""

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id)

        # User preferences table for email notifications
        self.user_preferences_table = dynamodb.Table(
            self,
            "UserPreferencesTable",
            table_name="biormika-user-preferences",
            partition_key=dynamodb.Attribute(
                name="user_id",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,  # For dev - change to RETAIN for production
            point_in_time_recovery=True,
        )

        # Analysis jobs table for tracking HFO processing
        self.analysis_jobs_table = dynamodb.Table(
            self,
            "AnalysisJobsTable",
            table_name="biormika-analysis-jobs",
            partition_key=dynamodb.Attribute(
                name="job_id",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.PAY_PER_REQUEST,
            removal_policy=RemovalPolicy.DESTROY,  # For dev - change to RETAIN for production
            point_in_time_recovery=True,
            stream=dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,  # Enable DynamoDB Streams for triggers
        )

        # Add GSI for querying jobs by user email
        self.analysis_jobs_table.add_global_secondary_index(
            index_name="UserEmailIndex",
            partition_key=dynamodb.Attribute(
                name="user_email",
                type=dynamodb.AttributeType.STRING
            ),
            sort_key=dynamodb.Attribute(
                name="created_at",
                type=dynamodb.AttributeType.STRING
            ),
            projection_type=dynamodb.ProjectionType.ALL,
        )

        # Add GSI for querying jobs by status
        self.analysis_jobs_table.add_global_secondary_index(
            index_name="StatusIndex",
            partition_key=dynamodb.Attribute(
                name="status",
                type=dynamodb.AttributeType.STRING
            ),
            sort_key=dynamodb.Attribute(
                name="created_at",
                type=dynamodb.AttributeType.STRING
            ),
            projection_type=dynamodb.ProjectionType.ALL,
        )

        # Add GSI for batch jobs
        self.analysis_jobs_table.add_global_secondary_index(
            index_name="BatchJobIndex",
            partition_key=dynamodb.Attribute(
                name="batch_id",
                type=dynamodb.AttributeType.STRING
            ),
            sort_key=dynamodb.Attribute(
                name="created_at",
                type=dynamodb.AttributeType.STRING
            ),
            projection_type=dynamodb.ProjectionType.ALL,
        )

    def grant_read_write(self, principal: iam.IGrantable) -> None:
        """Grant read/write permissions to both tables."""
        self.user_preferences_table.grant_read_write_data(principal)
        self.analysis_jobs_table.grant_read_write_data(principal)

    def grant_stream_read(self, principal: iam.IGrantable) -> None:
        """Grant permission to read from the analysis jobs stream."""
        self.analysis_jobs_table.grant_stream_read(principal)