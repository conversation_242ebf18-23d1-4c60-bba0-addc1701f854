# Project Configuration
PROJECT_NAME = "Biormika"
PURPOSE = "EDF File Storage and Management"
STACK_NAME = "BiormikaStack"
STACK_DESCRIPTION = "Infrastructure for Biormika EDF file management and HFO analysis"
ENVIRONMENT = "production"

# AWS Configuration
AWS_PROFILE = "biormika"
AWS_REGION = "us-east-1"

# File Configuration
ALLOWED_FILE_EXTENSIONS = [".edf", ".EDF"]
MAX_FILE_SIZE_MB = 1024
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
EDF_FILES_PREFIX = "edf-files"

# Network Configuration
VITE_DEV_PORT = 5173
REACT_ALT_PORT = 3000
FASTAPI_PORT = 8000

# CORS Configuration
DEFAULT_ALLOWED_ORIGINS = [
    f"http://localhost:{VITE_DEV_PORT}",
    f"http://localhost:{REACT_ALT_PORT}",
    f"http://localhost:{FASTAPI_PORT}",
]

# S3 Configuration
S3_CORS_MAX_AGE_SECONDS = 3600
S3_MULTIPART_UPLOAD_CLEANUP_DAYS = 1
S3_CORS_EXPOSED_HEADERS = [
    "ETag",
    "x-amz-server-side-encryption",
    "x-amz-request-id",
    "x-amz-id-2",
]

# Lambda Configuration
LAMBDA_RUNTIME_PYTHON = "PYTHON_3_11"
LAMBDA_ARCHITECTURE = "ARM_64"
LAMBDA_TIMEOUT_SECONDS = 30
LAMBDA_MEMORY_SIZE_MB = 512
LAMBDA_LOG_RETENTION_DAYS = 7
LAMBDA_LOG_GROUP_NAME = "/aws/lambda/biormika-api"
LAMBDA_HANDLER = "lambda_handler.handler"

# API Gateway Configuration
API_STAGE_NAME = "prod"
API_THROTTLE_RATE_LIMIT = 100
API_THROTTLE_BURST_LIMIT = 200
API_CORS_MAX_AGE_HOURS = 1

# S3 IAM Actions
S3_ACTIONS_BASE = [
    "s3:GetObject",
    "s3:PutObject",
    "s3:DeleteObject",
    "s3:ListBucket",
]

S3_ACTIONS_MULTIPART = [
    "s3:CreateMultipartUpload",
    "s3:UploadPart",
    "s3:CompleteMultipartUpload",
    "s3:AbortMultipartUpload",
]

S3_ACTIONS_READ_WRITE = [
    *S3_ACTIONS_BASE,
    "s3:GetObjectAttributes",
    "s3:HeadObject",
    *S3_ACTIONS_MULTIPART,
    "s3:ListBucketMultipartUploads",
]

S3_ACTIONS_PRESIGNED = [
    *S3_ACTIONS_BASE,
    *S3_ACTIONS_MULTIPART,
]
